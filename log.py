import logging
import os
import re
import threading
import time
import traceback

from collections import deque
from html import escape
from logging.handlers import RotatingFileHandler

from config import Config

# 禁用uvicorn和fastapi的默认日志
logging.getLogger("uvicorn").propagate = False
logging.getLogger("uvicorn.access").propagate = False
logging.getLogger("fastapi").propagate = False
logging.getLogger("websockets").propagate = False
logging.getLogger("websockets.protocol").propagate = False

# 专门针对WebSocket DEBUG日志的处理
websockets_logger = logging.getLogger("websockets")
websockets_protocol_logger = logging.getLogger("websockets.protocol")
# 设置WebSocket相关日志级别为INFO，避免DEBUG日志
websockets_logger.setLevel(logging.INFO)
websockets_protocol_logger.setLevel(logging.INFO)

class NoiseLogFilter(logging.Filter):
    """
    自定义日志过滤器，屏蔽噪音日志
    """
    def __init__(self):
        super().__init__()
        # 需要屏蔽的日志内容关键词
        self.blocked_patterns = [
            "GET /web HTTP/1.1",
            "POST /do?random=",
            "GET /basic HTTP/1.1",
            "GET /static/",
            "WebSocket /message",
            "connection open",
            "connection is CONNECTING",
            "connection is OPEN",
            "< TEXT",
            "> TEXT",
            "< GET /message",
            "< host:",
            "< connection:",
            "< user-agent:",
            "< upgrade:",
            "< origin:",
            "< sec-websocket",
            "< cookie:",
            "> HTTP/1.1 101",
            "> Upgrade:",
            "> Connection:",
            "> Sec-WebSocket",
            "> date:",
            "> server:",
            "sending keepalive ping",
            "received keepalive pong",
            "< PONG",
            "> PING"
        ]

    def filter(self, record):
        """
        过滤日志记录
        """
        message = record.getMessage()
        # 检查是否包含需要屏蔽的内容
        for pattern in self.blocked_patterns:
            if pattern in message:
                return False  # 屏蔽此日志
        return True  # 允许此日志通过

lock = threading.Lock()

LOG_QUEUE = deque(maxlen=200)
LOG_INDEX = 0


class Logger:
    logger = None
    __instance = {}
    __config = None

    __loglevels = {
        "info": logging.INFO,
        "debug": logging.DEBUG,
        "error": logging.ERROR
    }

    def __init__(self, module):
        self.logger = logging.getLogger(module)
        self.__config = Config()
        logtype = self.__config.get_config('app').get('logtype') or "console"
        loglevel = self.__config.get_config('app').get('loglevel') or "info"
        self.logger.setLevel(level=self.__loglevels.get(loglevel))
        if logtype == "server":
            logserver = self.__config.get_config('app').get('logserver', '').split(':')
            if logserver:
                logip = logserver[0]
                if len(logserver) > 1:
                    logport = int(logserver[1] or '514')
                else:
                    logport = 514
                log_server_handler = logging.handlers.SysLogHandler((logip, logport),
                                                                    logging.handlers.SysLogHandler.LOG_USER)
                log_server_handler.setFormatter(logging.Formatter('%(filename)s: %(message)s'))
                self.logger.addHandler(log_server_handler)
        elif logtype == "file":
            # 记录日志到文件
            logpath = os.environ.get('NASTOOL_LOG') or self.__config.get_config('app').get('logpath') or ""
            if logpath:
                if not os.path.exists(logpath):
                    os.makedirs(logpath)
                log_file_handler = RotatingFileHandler(filename=os.path.join(logpath, module + ".txt"),
                                                       maxBytes=5 * 1024 * 1024,
                                                       backupCount=3,
                                                       encoding='utf-8')
                log_file_handler.setFormatter(logging.Formatter('%(asctime)s\t%(levelname)s: %(message)s'))
                self.logger.addHandler(log_file_handler)
        # 记录日志到终端
        log_console_handler = logging.StreamHandler()
        log_console_handler.setFormatter(logging.Formatter('%(asctime)s\t%(levelname)s: %(message)s'))
        self.logger.addHandler(log_console_handler)

    @staticmethod
    def get_instance(module):
        if not module:
            module = "run"
        if Logger.__instance.get(module):
            return Logger.__instance.get(module)
        with lock:
            Logger.__instance[module] = Logger(module)
        return Logger.__instance.get(module)


def __append_log_queue(level, text):
    global LOG_INDEX, LOG_QUEUE
    with lock:
        text = escape(text)
        if text.startswith("【"):
            source = re.findall(r"(?<=【).*?(?=】)", text)[0]
            text = text.replace(f"【{source}】", "")
        else:
            source = "System"
        LOG_QUEUE.append({
            "time": time.strftime('%H:%M:%S', time.localtime(time.time())),
            "level": level,
            "source": source,
            "text": text})
        LOG_INDEX += 1


def debug(text, module=None):
    return Logger.get_instance(module).logger.debug(text)


def info(text, module=None):
    __append_log_queue("INFO", text)
    return Logger.get_instance(module).logger.info(text)


def error(text, module=None):
    __append_log_queue("ERROR", text)
    return Logger.get_instance(module).logger.error(text)

def exception(text, e, module=None):
    text = f"{text}\nException: {str(e)}\nCallstack:\n{traceback.format_exc()}\n"
    __append_log_queue("ERROR", text)
    return Logger.get_instance(module).logger.error(text)

def warn(text, module=None):
    __append_log_queue("WARN", text)
    return Logger.get_instance(module).logger.warning(text)


def console(text):
    __append_log_queue("INFO", text)
    print(text)


def setup_fastapi_logging():
    """
    配置FastAPI和Uvicorn的日志系统，使其使用我们的日志配置
    """
    # 获取配置
    config = Config()
    loglevel = config.get_config('app').get('loglevel') or "info"

    # 直接映射日志级别，而不是使用Logger类的私有变量
    log_levels = {
        "info": logging.INFO,
        "debug": logging.DEBUG,
        "error": logging.ERROR
    }
    log_level = log_levels.get(loglevel, logging.INFO)

    # 创建噪音日志过滤器
    noise_filter = NoiseLogFilter()

    # 配置uvicorn和fastapi的日志
    for logger_name in ["uvicorn", "uvicorn.access", "fastapi", "websockets", "websockets.protocol"]:
        logger = logging.getLogger(logger_name)
        logger.handlers.clear()  # 清除现有的处理器

        # 对于websockets和访问日志，设置更高的级别或完全禁用
        if logger_name.startswith("websockets"):
            logger.setLevel(logging.WARNING)  # 只显示警告及以上
            logger.propagate = False
        elif logger_name == "uvicorn.access":
            logger.setLevel(logging.CRITICAL)  # 基本屏蔽访问日志
            logger.disabled = True  # 完全禁用
        else:
            logger.setLevel(log_level)

        # 添加我们自己的处理器（如果没有被禁用）
        if not logger.disabled:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s\t%(levelname)s: [%(name)s] %(message)s'))
            handler.addFilter(noise_filter)  # 添加噪音过滤器
            logger.addHandler(handler)

    # 为根日志器的所有处理器添加过滤器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        handler.addFilter(noise_filter)

    print("已应用日志噪音过滤器，屏蔽HTTP访问和WebSocket调试日志")
