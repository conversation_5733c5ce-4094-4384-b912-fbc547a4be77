<div class="page-body">
  <div class="container-xl">
    <div id='calendar'></div>
  </div>
</div>

<script type="text/javascript">
  calendar = new FullCalendar.Calendar(document.getElementById('calendar'), {
    themeSystem: 'bootstrap',
    bootstrapFontAwesome: false,
    stickyHeaderDates: false,
    fixedWeekCount: false,
    expandRows: true,
    height: 'auto',
    locale: 'zh-cn',
    headerToolbar: {
      left: 'prev,next',
      center: 'title',
      right: 'dayGridWeek,dayGridMonth,listMonth'
    },
    defaultView: 'dayGridWeek',
    initialDate: '{{ Today }}',
    navLinks: true,
    editable: false,
    dayMaxEvents: true,
    selectable: false,
    events: [],
    eventDisplay: 'list-item',
    eventClick: function (info) {
      const eventData = info.event.extendedProps;
      let type;
      if (eventData.type == "电影") {
        type = "MOV";
      } else {
        type = "TV";
      }
      show_mediainfo_modal(type, info.event.title, eventData.year, info.event.id, "rss_calendar", eventData.rssid);
    },
    eventContent: function (info) {
      const eventData = info.event.extendedProps;
      let border_color;
      if (eventData.type == '电影') {
        border_color = 'bg-green';
      } else {
        border_color = 'bg-primary';
      }
      const innerText = `
        <a class="card card-link" href="javascript:void(0)">
          <div class="card-status-start ${border_color}"></div>
          <div class="card-body" style="padding: 0.5rem 1rem; overflow: hidden;">
            <div class="row">
              <div class="col-auto rounded" style="width:40px; height:50px; background-image:url(${eventData.poster}); background-size:cover;"></div>
              <div class="col media_calendar_item_info d-none d-xl-block">
                <div class="font-weight-medium" style="white-space:pre-wrap;word-break:break-all;"><strong>${info.event.title}</strong></div>
                <div class="text-muted">${eventData.type} 评分: ${eventData.vote_average}</div>
              </div>
            </div>
          </div>
        </a>
      `;
      return {html: innerText};
    }
  });
  calendar.render();
</script>
<script type="text/javascript">
  //查询订阅电影的上映日期
  RssMovieItems = {{ RssMovieItems| safe }};

  function init_rss_movies_event() {
    for (let i = 0; i < RssMovieItems.length; i++) {
      ajax_post("movie_calendar_data",
          {
            "id": RssMovieItems[i].id,
            "rssid": RssMovieItems[i].rssid
          }, function (ret) {
        if (ret.code === 0) {
          calendar.addEvent({
            id: ret.id,
            title: ret.title,
            start: ret.start,
            allDay: true,
            poster: ret.poster,
            vote_average: ret.vote_average,
            year: ret.year,
            type: ret.type,
            rssid: ret.rssid
          });
        }
      });
    }
  };
  //查询订阅电视剧各集的上映日期
  RssTvItems = {{ RssTvItems| safe }};

  function init_rss_tvs_event() {
    for (let i = 0; i < RssTvItems.length; i++) {
      ajax_post("tv_calendar_data", {
        "id": RssTvItems[i].id,
        "season": RssTvItems[i].season,
        "name": RssTvItems[i].name,
        "rssid": RssTvItems[i].rssid
      }, function (ret) {
        if (ret.code === 0) {
          for (let i = 0; i < ret.events.length; i++) {
            calendar.addEvent({
              id: ret.events[i].id,
              title: ret.events[i].title,
              start: ret.events[i].start,
              allDay: true,
              poster: ret.events[i].poster,
              vote_average: ret.events[i].vote_average,
              year: ret.events[i].year,
              type: ret.events[i].type,
              rssid: ret.events[i].rssid
            });
          }
        }
      });
    }
  };
  //拉取订阅电影
  init_rss_movies_event();
  //拉取订阅电视剧
  init_rss_tvs_event();
</script>