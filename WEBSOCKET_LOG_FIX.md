# WebSocket日志噪音修复指南

## 问题描述

在使用NAStool时，控制台经常出现大量的WebSocket DEBUG日志，如：

```
DEBUG:    < TEXT '{"lst_time":"2025-06-24 00:20:25"}' [34 bytes] 
DEBUG:    > TEXT '{"lst_time": "2025-06-24 00:20:25", "message": []}' [50 bytes] 
DEBUG:    < TEXT '{"lst_time":"2025-06-24 00:20:25"}' [34 bytes] 
DEBUG:    > TEXT '{"lst_time": "2025-06-24 00:20:25", "message": []}' [50 bytes] 
```

这些日志是由前端每5秒轮询消息产生的，虽然是正常行为，但会产生大量日志噪音。

## 解决方案

### 方案1：配置文件修改（推荐）

1. **修改配置文件** `config/config.yaml`：
   ```yaml
   app:
     # 设置日志级别为info
     loglevel: info
     # 关闭debug模式
     debug: false
   ```

2. **重启应用**

### 方案2：Docker环境

对于Docker部署，我们提供了自动修复脚本：

1. **使用修复后的镜像**：
   - 新版本的Docker镜像已包含WebSocket日志修复
   - 启动时会自动检查并修复配置文件中的日志设置

2. **手动修复**：
   ```bash
   # 进入容器
   docker exec -it nas-tools bash
   
   # 修改配置文件
   sed -i 's/debug: true/debug: false/g' /config/config.yaml
   sed -i 's/loglevel: debug/loglevel: info/g' /config/config.yaml
   
   # 重启容器
   docker restart nas-tools
   ```

### 方案3：环境变量控制

在Docker compose中添加环境变量：

```yaml
environment:
  - NASTOOL_LOG_LEVEL=info
  - WEBSOCKETS_LOG_LEVEL=INFO
```

## 技术细节

### 问题根源

1. **前端轮询**：前端每5秒通过WebSocket请求新消息
2. **DEBUG日志**：uvicorn的WebSocket实现在DEBUG级别记录所有消息
3. **配置问题**：`debug: true` 会强制uvicorn使用DEBUG日志级别

### 修复原理

1. **日志级别控制**：将WebSocket相关日志器设置为INFO级别
2. **配置优化**：确保应用配置使用合适的日志级别
3. **启动时修复**：在应用启动时强制设置正确的日志级别

### 代码修改

主要修改了以下文件：

1. `log.py` - 添加WebSocket日志级别控制
2. `run.py` - 优化uvicorn日志配置
3. `web/fastapi_app.py` - 添加启动时日志修复
4. `docker/entrypoint.sh` - Docker环境配置检查
5. `web/static/js/functions.js` - 增加轮询间隔（3秒→5秒）

## 验证修复

修复后，您应该看到：

1. **启动日志**显示正确的日志级别：
   ```
   正在启动FastAPI应用...使用配置: {'log_level': 'info'}
   已禁用WebSocket DEBUG日志
   ```

2. **运行时日志**只显示有意义的INFO级别日志：
   ```
   INFO:     127.0.0.1:14920 - "WebSocket /message" [accepted]
   INFO:     connection open
   ```

3. **不再出现**频繁的DEBUG级别WebSocket消息日志

## 注意事项

1. **生产环境**建议始终使用`loglevel: info`
2. **调试时**如需DEBUG日志，可临时修改配置
3. **Docker环境**重启容器后修复会自动生效
4. **配置备份**修改前建议备份原配置文件

## 常见问题

**Q: 修改后仍有DEBUG日志？**
A: 检查配置文件中是否还有`debug: true`，确保重启了应用

**Q: Docker环境修改无效？**
A: 确保配置文件挂载正确，检查容器内的配置文件是否已修改

**Q: 会影响功能吗？**
A: 不会，只是减少了日志输出，所有功能正常

## 联系支持

如果问题仍然存在，请提供：
1. 配置文件内容（隐藏敏感信息）
2. 启动日志
3. 部署方式（Docker/直接运行）
