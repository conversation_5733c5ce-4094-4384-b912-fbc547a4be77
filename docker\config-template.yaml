# Docker环境专用配置模板
# 此文件用于确保Docker环境中的日志配置正确
app:
  # 【日志记录类型】：server、file、console
  # Docker环境建议使用console
  logtype: console
  # 【日志级别】：info、debug、error
  # 生产环境建议使用info，避免过多DEBUG日志
  loglevel: info
  # 【WEB管理界面监听地址】
  web_host: 0.0.0.0
  # 【WEB管理界面端口】
  web_port: 3000
  # 【WEB管理页面登录用户】
  login_user: admin
  # 【WEB管理页面登录密码】
  login_password: password
  # Debug mode - 生产环境建议关闭
  debug: false
  # 【TMDB API KEY】
  rmt_tmdbkey:
  # 【使用TMDB服务器域名】
  tmdb_domain: api.tmdb.org
  # 【TMDB图片代理地址】
  tmdb_image_url: https://image.tmdb.org
  # 【TMDB匹配模式】
  rmt_match_mode: normal
  # 【本系统的WEB的外网地址】
  domain: ''
  # 【UserAgent】
  user_agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  # 是否启用redis缓存
  redis_enable: true
  # redis服务的ip和地址
  redis_addr: ''
  # 自建OCR服务的地址
  ocr_url: ''
  # 【登录界面壁纸】
  wallpaper: bing
  # 开启后，只有Releases更新，才会有更新提示
  releases_update_only: false

media:
  # 【媒体库电影文件存放目录】
  movie_path:
  # 【媒体库电视剧文件存放目录】
  tv_path:
  # 【媒体库动漫文件单独存放目录】
  anime_path:
  # 【无法识别时转移存放的目录】
  unknown_path:
  # 【二级分类开关】
  category: default-category
  # 【媒体库管理软件】
  media_server: jellyfin
  # 【媒体库数据同步周期】
  mediasync_interval: '3'
  # 【转移到媒体库的最小文件大小】
  min_filesize: 150
  # 【文件名转移忽略词】
  ignored_files:
  # 【文件路径转移忽略词】
  ignored_paths:
  # 【洗版开关】
  filesize_cover: true
  # 【电影命名定义】
  movie_name_format: '{title} ({year})/{title}-{part} ({year}) - {videoFormat}'
  # 【电视剧命名定义】
  tv_name_format: '{title} ({year})/Season {season}/{title}-{part} - {season_episode} - 第{episode}集'
  # 【刮削元数据及图片】
  nfo_poster: false
  # 文件管理、自定义识别等默认路径
  media_default_path:
  # 默认文件转移方式
  default_rmt_mode: move
  # 默认TMDB信息语种
  tmdb_language: zh

# 配置Jellyfin服务器信息
jellyfin:
  # 【Jellyfin服务器IP地址和端口】
  host: http://127.0.0.1:8096
  # 【Jellyfin ApiKey】
  api_key:
  # 【Jellyfin媒体播放地址和端口】
  play_host: http://127.0.0.1:8096

# 【配置站点搜索信息】
pt:
  # 【远程搜索自动择优下载开关】
  search_auto: true
  # 【远程下载不完整自动订阅】
  search_no_result_rss: false
  # 【RSS订阅开关】
  pt_check_interval: 1800
  # 【定量搜索开关】
  search_rss_interval: '3'
  # 【下载优先规则】
  download_order: site
  # 【搜索结果数量限制】
  site_search_result_num: 100
  # 站点数据刷新时间
  ptrefresh_date_cron: ''

# 【配置安全】
security:
  # 【媒体服务器webhook允许ip范围】
  media_server_webhook_allow_ip:
    ipv4: 0.0.0.0/0
    ipv6: ::/0
  # 【Telegram webhook允许ip范围】
  telegram_webhook_allow_ip:
    ipv4: 127.0.0.1
    ipv6: ::/0
  # 【Synology Chat webhook允许ip范围】
  synology_webhook_allow_ip:
    ipv4: 127.0.0.1
    ipv6: ::/0
  # 【API认证密钥】
  api_key:
  # 【是否验证apikey】
  check_apikey: false

# 【实验室】
laboratory:
  # 【识别增强】关键字猜想
  search_keyword: false
  # 【WEB识别增强】通过TMDB WEB检索
  search_tmdbweb: false
  # 【ChatGPT识别增强】通过ChatGPT识别文件名
  chatgpt_enable: false
  # 【TMDB缓存过期策略】
  tmdb_cache_expire: true
  # 【默认搜索豆瓣资源】
  use_douban_titles: false
  # 【精确搜索使用英文名称】
  search_en_title: false
