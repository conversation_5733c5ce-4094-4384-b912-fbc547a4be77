{% import 'macro/svg.html' as SVG %}
<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_add_rssparser_modal()" class="btn btn-primary d-none d-sm-inline-block">
            {{ SVG.plus() }}
            新增解析器
          </a>
          <a href="javascript:show_add_rssparser_modal()" class="btn btn-primary d-sm-none btn-icon">
            {{ SVG.plus() }}
          </a>
          <a href="javascript:navmenu('user_rss')" class="btn d-none d-sm-inline-block" title="返回">
            {{ SVG.arrow_back_up() }}
            返回
          </a>
          <a href="javascript:navmenu('user_rss')" class="btn d-sm-none btn-icon" title="返回">
            {{ SVG.arrow_back_up() }}
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-body border-bottom py-3">
            <div class="d-flex">
              <div class="text-muted">
                共 {{ Count }} 条记录
              </div>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-vcenter card-table table-hover table-striped">
              <thead>
              <tr>
                <th>名称</th>
                <th>类型</th>
                <th>附加参数</th>
                <th class="w-3"></th>
              </tr>
              </thead>
              <tbody>
              {% if RssParsers %}
                {% for RssParser in RssParsers %}
                  <tr>
                    <td>{{ RssParser.name or '' }}</td>
                    <td><span class="badge me-2">{{ RssParser.type or '' }}</span></td>
                    <td>{{ RssParser.params or '' }}</td>
                    <td>
                      <div class="d-flex">
                        <a href="javascript:show_edit_rssparser_modal('{{ RssParser.id }}')" class="btn-action ms-1"
                           title="编辑解析器">
                          {{ SVG.edit() }}
                        </a>
                        <a href="javascript:delete_rssparser('{{ RssParser.id }}')" class="btn-action ms-1" title="删除解析器">
                          {{ SVG.x() }}
                        </a>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td colspan="4" align="center">没有数据</td>
                </tr>
              {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-userrss-parser" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-filterrule-title">新增解析器</h5>
        <input type="hidden" id="rssparser_id"/>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">名称</label>
              <input type="text" class="form-control" id="rssparser_name" placeholder="自定义名称">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">类型 <span class="form-help"
                                                   title="支持XML及JSON，需要分别按XPath及JsonPath的语法维护解析格式"
                                                   data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="rssparser_type">
                <option value="XML" selected>XML</option>
                <option value="JSON">JSON</option>
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <div class="mb-3">
              <label class="form-label">解析格式 <span class="form-help"
                                                       title="根据RSS报文的内容，编辑Json格式解析配置，以获取报文中的数据以供订阅程序处理；如类型为XML则需要是XPath语法，如类型为JSON则需要是JsonPath语法；支持的解析内容包括：list(获取列表) title(标题) enclosure(种子下载链接) description(种子描述) size(种子大小) type(类型：movie/tv) year(发行年份)，没有或不需要的可不配置或配置为空，具体参考现有示例；解析器可以尽可能解析更多的信息，由订阅程序将按需使用"
                                                       data-bs-toggle="tooltip">?</span></label>
              <div id="rssparser_format" class="form-control" style="height: 20rem"></div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <div class="mb-3">
              <label class="form-label">附加参数 <span class="form-help" title="需要附加到RSS链接中的参数"
                                                       data-bs-toggle="tooltip">?</span></label>
              <input type="text" class="form-control" id="rssparser_params" value="" placeholder="">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="rssparser_close_btn" class="btn btn-link" data-bs-dismiss="modal">
          取消
        </button>
        <button id="add_or_edit_rssparser_btn" class="btn btn-primary ms-auto"
                onclick="add_or_edit_rssparser()">
          新增
        </button>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">

  // 初始化JsonEditor
  json_editor = ace.edit("rssparser_format");
  localStorage.getItem("tablerTheme") === "dark" ? json_editor.setTheme("ace/theme/one_dark") : json_editor.setTheme("ace/theme/xcode");
  json_editor.session.setMode("ace/mode/json");
  json_editor.setOptions({
    fontFamily: "Consolas, Monaco, monospace",
    fontSize: "10pt"
  });

  // 打开新增窗口
  function show_add_rssparser_modal() {
    $("#rssparser_id").val('');
    $("#add_or_edit_rssparser_btn").text('新增');
    $("#modal-userrss-parser").modal('show');
  }

  // 新增解析器
  function add_or_edit_rssparser() {
    if (!$("#rssparser_name").val()) {
      $("#rssparser_name").addClass("is-invalid");
      return;
    } else {
      $("#rssparser_name").removeClass("is-invalid");
    }
    try {
      const params = {
        id: $("#rssparser_id").val(),
        name: $("#rssparser_name").val(),
        type: $("#rssparser_type").val(),
        format: json_editor.getValue(),
        params: $("#rssparser_params").val()
      };
      ajax_post("update_rssparser", params, function (ret) {
        $("#modal-userrss-parser").modal('hide');
        window_history_refresh();
      });
    } catch(err) {
        console.log(err);
    }
  }

  // 删除解析器
  function delete_rssparser(id) {
    show_ask_modal("是否确认删除该解析器？ ", function () {
      hide_ask_modal();
      const params = {"id": id};
      ajax_post("delete_rssparser", params, function (ret) {
        window_history_refresh();
      });
    });
  }

  // 编辑解析器
  function show_edit_rssparser_modal(id) {
    $("#rssparser_id").val(id);
    ajax_post("get_rssparser", {id: id}, function (ret) {
      if (ret.code == 0) {
        $("#rssparser_name").val(ret.detail.name);
        $("#rssparser_type").val(ret.detail.type);
        try {
          json_editor.setValue(ret.detail.format);
        } catch(err) {
          console.log(err);
        }
        $("#rssparser_params").val(ret.detail.params);
        $("#add_or_edit_rssparser_btn").text('保存');
        $("#modal-userrss-parser").modal('show');
      }
    });
  }
</script>