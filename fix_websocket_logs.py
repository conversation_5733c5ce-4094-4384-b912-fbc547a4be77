#!/usr/bin/env python3
"""
WebSocket日志修复脚本
用于强制禁用WebSocket的DEBUG日志，解决控制台日志噪音问题
"""

import logging
import sys
import os

class NoiseLogFilter(logging.Filter):
    """
    自定义日志过滤器，屏蔽噪音日志
    """
    def __init__(self):
        super().__init__()
        # 需要屏蔽的日志内容关键词
        self.blocked_patterns = [
            "GET /web HTTP/1.1",
            "POST /do?random=",
            "GET /basic HTTP/1.1",
            "GET /static/",
            "WebSocket /message",
            "connection open",
            "connection is CONNECTING",
            "connection is OPEN",
            "< TEXT",
            "> TEXT",
            "< GET /message",
            "< host:",
            "< connection:",
            "< user-agent:",
            "< upgrade:",
            "< origin:",
            "< sec-websocket",
            "< cookie:",
            "> HTTP/1.1 101",
            "> Upgrade:",
            "> Connection:",
            "> Sec-WebSocket",
            "> date:",
            "> server:",
            "sending keepalive ping",
            "received keepalive pong",
            "< PONG",
            "> PING"
        ]

    def filter(self, record):
        """
        过滤日志记录
        """
        message = record.getMessage()
        # 检查是否包含需要屏蔽的内容
        for pattern in self.blocked_patterns:
            if pattern in message:
                return False  # 屏蔽此日志
        return True  # 允许此日志通过

def fix_websocket_logs():
    """
    彻底屏蔽WebSocket和HTTP访问日志，避免日志噪音
    """
    print("正在屏蔽WebSocket和HTTP访问日志...")

    # 需要屏蔽的日志器列表
    loggers_to_silence = [
        "websockets",
        "websockets.protocol",
        "websockets.server",
        "websockets.client",
        "uvicorn.protocols.websockets",
        "uvicorn.protocols.websockets.websockets_impl",
        "uvicorn.access",  # HTTP访问日志
        "uvicorn.protocols.http",  # HTTP协议日志
    ]

    for logger_name in loggers_to_silence:
        logger = logging.getLogger(logger_name)
        # 设置为WARNING级别，屏蔽INFO和DEBUG日志
        logger.setLevel(logging.WARNING)
        # 清除现有的处理器
        logger.handlers.clear()
        # 设置不传播到父级日志器
        logger.propagate = False
        print(f"已屏蔽 {logger_name} 的INFO/DEBUG日志")

    # 特别处理uvicorn的访问日志
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.disabled = True  # 完全禁用访问日志
    print("已完全禁用uvicorn访问日志")

    # 设置根日志级别
    root_logger = logging.getLogger()
    if root_logger.level == logging.DEBUG:
        root_logger.setLevel(logging.INFO)
        print("已将根日志级别从DEBUG调整为INFO")

    # 添加自定义过滤器到根日志器
    noise_filter = NoiseLogFilter()
    root_logger = logging.getLogger()

    # 为所有处理器添加过滤器
    for handler in root_logger.handlers:
        handler.addFilter(noise_filter)

    # 为控制台处理器添加过滤器
    console_handlers = [h for h in logging.getLogger().handlers if isinstance(h, logging.StreamHandler)]
    for handler in console_handlers:
        handler.addFilter(noise_filter)

    print("已添加噪音日志过滤器")
    print("日志屏蔽完成 - 将不再显示HTTP访问和WebSocket调试日志")

def setup_uvicorn_logging():
    """
    配置uvicorn的日志设置，屏蔽访问日志
    """
    # 获取uvicorn相关的日志器
    uvicorn_loggers = {
        "uvicorn": logging.INFO,           # 保留基本信息
        "uvicorn.error": logging.ERROR,    # 只显示错误
        "uvicorn.access": logging.CRITICAL # 屏蔽访问日志
    }

    for logger_name, level in uvicorn_loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
        if logger_name == "uvicorn.access":
            logger.disabled = True  # 完全禁用访问日志
            print(f"已完全禁用 {logger_name}")
        else:
            print(f"已设置 {logger_name} 日志级别为 {logging.getLevelName(level)}")

    print("Uvicorn日志配置完成 - 已屏蔽HTTP访问日志")

if __name__ == "__main__":
    fix_websocket_logs()
    setup_uvicorn_logging()
