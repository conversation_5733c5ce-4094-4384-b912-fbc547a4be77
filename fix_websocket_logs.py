#!/usr/bin/env python3
"""
WebSocket日志修复脚本
用于强制禁用WebSocket的DEBUG日志，解决控制台日志噪音问题
"""

import logging
import sys
import os

def fix_websocket_logs():
    """
    强制设置WebSocket相关日志级别为INFO，避免DEBUG日志干扰
    """
    print("正在修复WebSocket日志设置...")
    
    # 设置WebSocket相关的日志级别
    loggers_to_fix = [
        "websockets",
        "websockets.protocol", 
        "websockets.server",
        "websockets.client",
        "uvicorn.protocols.websockets",
        "uvicorn.protocols.websockets.websockets_impl"
    ]
    
    for logger_name in loggers_to_fix:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)
        # 清除现有的处理器
        logger.handlers.clear()
        # 设置不传播到父级日志器
        logger.propagate = False
        print(f"已设置 {logger_name} 日志级别为 INFO")
    
    # 设置根日志级别
    root_logger = logging.getLogger()
    if root_logger.level == logging.DEBUG:
        root_logger.setLevel(logging.INFO)
        print("已将根日志级别从DEBUG调整为INFO")
    
    print("WebSocket日志修复完成")

def setup_uvicorn_logging():
    """
    配置uvicorn的日志设置
    """
    # 获取uvicorn相关的日志器
    uvicorn_loggers = [
        "uvicorn",
        "uvicorn.error", 
        "uvicorn.access"
    ]
    
    for logger_name in uvicorn_loggers:
        logger = logging.getLogger(logger_name)
        # 只保留INFO级别及以上的日志
        logger.setLevel(logging.INFO)
    
    print("Uvicorn日志配置完成")

if __name__ == "__main__":
    fix_websocket_logs()
    setup_uvicorn_logging()
