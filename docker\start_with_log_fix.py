#!/usr/bin/env python3
"""
Docker环境专用启动脚本
在启动应用前修复WebSocket日志问题
"""

import os
import sys
import logging
import subprocess

def fix_websocket_logging():
    """
    修复WebSocket日志问题
    """
    print("正在修复WebSocket日志设置...")
    
    # 设置环境变量来控制日志级别
    os.environ['WEBSOCKETS_LOG_LEVEL'] = 'INFO'
    os.environ['UVICORN_LOG_LEVEL'] = 'info'
    
    # 强制设置WebSocket相关日志级别
    websocket_loggers = [
        "websockets",
        "websockets.protocol",
        "websockets.server",
        "websockets.client", 
        "uvicorn.protocols.websockets"
    ]
    
    for logger_name in websocket_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)
        logger.propagate = False
    
    print("WebSocket日志修复完成")

def check_config_file():
    """
    检查并修复配置文件
    """
    config_path = os.environ.get('NASTOOL_CONFIG', '/config/config.yaml')
    
    if os.path.exists(config_path):
        print(f"检查配置文件: {config_path}")
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查并修复debug设置
        if 'debug: true' in content:
            print("发现debug: true，修改为debug: false")
            content = content.replace('debug: true', 'debug: false')
        
        # 检查并修复loglevel设置
        if 'loglevel: debug' in content:
            print("发现loglevel: debug，修改为loglevel: info")
            content = content.replace('loglevel: debug', 'loglevel: info')
        
        # 写回配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("配置文件检查完成")
    else:
        print(f"配置文件不存在: {config_path}")

def main():
    """
    主函数
    """
    print("Docker环境WebSocket日志修复启动脚本")
    
    # 修复日志设置
    fix_websocket_logging()
    
    # 检查配置文件
    check_config_file()
    
    # 启动主程序
    print("启动NAStool主程序...")
    try:
        # 使用exec替换当前进程
        os.execv(sys.executable, [sys.executable, 'run.py'])
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
