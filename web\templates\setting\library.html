{% import 'macro/svg.html' as SVG %}
{% import 'macro/form.html' as FORM %}

<style>
  .components-div {
    border-radius: 5px;
    border: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
    background-color: var(--tblr-card-bg);
    background-clip: border-box;
    margin: 10px;
  }
  .indexer-div {
    margin: 10px;
  }
    .form-label {
      width: 90px;
    }
  #builtin_save_btn {
    float: right;
  }
</style>

<div id="server-div" class="card components-div">
  <div class="card-header">
    <h2 class="page-title">媒体服务器</h2>
  </div>
  <!-- 业务页面代码 -->
  <div class="page-body">
    <div class="container-xl">
      <div class="d-grid gap-3 grid-normal-card">
        {% for Type, MediaServer in MediaServerConf.items() %}
          <a class="card card-link-pop p-0 rounded-3 overflow-hidden" href="#" data-bs-toggle="modal" data-bs-target="#modal-{{ Type }}">
            <div class="card card-sm card-link-pop {{ MediaServer.background }}">
              <div class="card-body">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="text-white avatar" style="background-image: url({{ MediaServer.img_url }})">
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">
                      <span id="service_btn">{{ MediaServer.name }}</span>
                    </div>
                  </div>
                  {% if Config.media.media_server == Type %}
                  <div class="col-auto align-self-center">
                    <span class="badge bg-green" title="已开启"></span> 正在使用
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </a>
        {% endfor %}
        </div>
    </div>
  </div>
  {% for Type, MediaServer in MediaServerConf.items() %}
  <div class="modal modal-blur fade" id="modal-{{ Type }}" tabindex="-1" role="dialog" aria-hidden="true"
       data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ MediaServer.name }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
        {{ FORM.gen_form_config_elements(Type, Config, MediaServer.config) }}
        </div>
        <div class="modal-footer">
          <a href="javascript:test_mediaserver_config('{{ Type }}')" id="{{ Type }}_test_btn" class="btn me-auto">
            测试
          </a>
          <a href="javascript:save_mediaserver_config('{{ Type }}')" id="{{ Type }}_save_btn" class="btn btn-primary">
            确定
          </a>
        </div>
      </div>
    </div>
  </div>
  {% endfor %}
</div>

<div id="lib-div" class="card components-div">
  <div class="card-header">
    <h2 class="page-title">媒体库</h2>
  </div>
  <!-- 业务页面代码 -->
  <div class="page-body">
    <div class="container-xl">
      <div class="row row-cards">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title"><strong>电影</strong></h3>
              <a href="javascript:show_directory_modal('movie')" class="btn btn-primary btn-icon ms-auto">
                {{ SVG.plus() }}
              </a>
            </div>
            <div class="table-responsive">
              <table class="table card-table table-vcenter text-nowrap datatable table-hover table-striped">
                <thead>
                <tr>
                  <th>目录</th>
                  <th class="w-3"></th>
                </tr>
                </thead>
                <tbody>
                {% if Config.media.movie_path %}
                  {% for path in Config.media.movie_path %}
                    <tr>
                      <td>
                        <input type="hidden" value="{{ path }}">
                        {{ path }}
                      </td>
                      <td>
                        <a href="javascript:sub_directory_config('movie', '{{ path }}')" title="删除目录">
                          {{ SVG.x() }}
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="2" align="center">未配置</td>
                  </tr>
                {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title"><strong>电视剧</strong></h3>
              <a href="javascript:show_directory_modal('tv')" class="btn btn-primary btn-icon ms-auto">
                {{ SVG.plus() }}
              </a>
            </div>
            <div class="table-responsive">
              <table class="table card-table table-vcenter text-nowrap datatable table-hover table-striped">
                <thead>
                <tr>
                  <th>目录</th>
                  <th class="w-3"></th>
                </tr>
                </thead>
                <tbody>
                {% if Config.media.tv_path %}
                  {% for path in Config.media.tv_path %}
                    <tr>
                      <td>
                        <input type="hidden" value="{{ path }}">
                        {{ path }}
                      </td>
                      <td>
                        <a href="javascript:sub_directory_config('tv', '{{ path }}')" title="删除目录">
                          {{ SVG.x() }}
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="2" align="center">未配置</td>
                  </tr>
                {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title"><strong>动漫</strong></h3>
              <a href="javascript:show_directory_modal('anime')" class="btn btn-primary btn-icon ms-auto">
                {{ SVG.plus() }}
              </a>
            </div>
            <div class="table-responsive">
              <table class="table card-table table-vcenter text-nowrap datatable table-hover table-striped">
                <thead>
                <tr>
                  <th>目录</th>
                  <th class="w-3"></th>
                </tr>
                </thead>
                <tbody>
                {% if Config.media.anime_path %}
                  {% for path in Config.media.anime_path %}
                    <tr>
                      <td>
                        <input type="hidden" value="{{ path }}">
                        {{ path }}
                      </td>
                      <td>
                        <a href="javascript:sub_directory_config('anime', '{{ path }}')" title="删除目录">
                          {{ SVG.x() }}
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="2" align="center">未配置</td>
                  </tr>
                {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title"><strong>未识别</strong></h3>
              <a href="javascript:show_directory_modal('unknown')" class="btn btn-primary btn-icon ms-auto">
                {{ SVG.plus() }}
              </a>
            </div>
            <div class="table-responsive">
              <table class="table card-table table-vcenter text-nowrap datatable table-hover table-striped">
                <thead>
                <tr>
                  <th>目录</th>
                  <th class="w-3"></th>
                </tr>
                </thead>
                <tbody>
                {% if Config.media.unknown_path %}
                  {% for path in Config.media.unknown_path %}
                    <tr>
                      <td>
                        <input type="hidden" value="{{ path }}">
                        {{ path }}
                      </td>
                      <td>
                        <a href="javascript:sub_directory_config('unknown', '{{ path }}')" title="删除目录">
                          {{ SVG.x() }}
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="2" align="center">未配置</td>
                  </tr>
                {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal modal-blur fade" id="modal-dirctory" tabindex="-1" role="dialog" aria-hidden="true"
       data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新增目录</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-12">
              <div class="mb-3">
                <label class="form-label">路径 <span class="form-help"
                                                     title="Emby/Jellyfin/Plex媒体库对应文件的路径，下载文件转移、目录同步未配置目的目录时，媒体文件将重命名转移到该目录"
                                                     data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="path_str" class="form-control filetree-folders-only" autocomplete="off">
                <input type="hidden" value="" id="path_type" class="form-control">
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
          <a href="javascript:add_directory_config()" id="directory_save_btn" class="btn btn-primary">确定</a>
        </div>
      </div>
    </div>
  </div>
</div>


<script type="text/javascript">
  // 当前处理的类型
  var currType = "";

  // 保存配置
  function save_config(type, func, test) {
    currType = type;
    const params = input_select_GetVal(`modal-${type}`);
    params['test'] = test || false;
    params['media.media_server'] = type;
    ajax_post("update_config", params, func);
  }

  //保存配置、关闭和刷新页面
  function save_mediaserver_config(type) {
    $("#modal-" + type).modal('hide');
    save_config(type, function (ret) {
      window_history_refresh();
    });
  }

  //保存配置和测试配置
  function test_mediaserver_config(type) {
    $("#" + type + "_test_btn").text("测试中...").attr("disabled", true);
    save_config(type, function (ret) {
      let command;
      {% for Type, MediaServer in MediaServerConf.items() %}
      if (currType === "{{ Type }}") {
        command = "{{ MediaServer.test_command }}";
      }
      {% endfor %}
      ajax_post("test_connection", {"command": command}, function (ret) {
        if (ret.code === 0) {
          $("#" + currType + "_test_btn").text("测试成功").attr("disabled", false);
        } else {
          $("#" + currType + "_test_btn").text("测试失败！").attr("disabled", false);
        }
      });
    }, true);
  }
  // 打开新增窗口
  function show_directory_modal(type) {
    $("#path_type").val(type);
    $("#modal-dirctory").modal('show');
  }

  // 新增目录
  function add_directory_config() {
    const type = $("#path_type").val();
    const value = $("#path_str").val();
    const params = {"oper": "add", "key": "media." + type + "_path", "value": value};
    $("#modal-dirctory").modal('hide');
    ajax_post("update_directory", params, function (ret) {
      window_history_refresh();
    });
  }

  // 删除目录
  function sub_directory_config(type, value) {
    const params = {"oper": "sub", "key": "media." + type + "_path", "value": value};
    ajax_post("update_directory", params, function (ret) {
      window_history_refresh();
    });
  }

</script>