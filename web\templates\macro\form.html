<!-- 生成配置对应的表单元素 -->
{% macro gen_form_config_elements(Type, Config, Fields) %}
  {% for FieldId, FieldAttr in Fields.items() %}
    {% if loop.index%2 == 1 %}
    <div class="row">
    {% endif %}
    <div class="{% if FieldAttr.type == "switch" %}col-12{% else %}col-lg{% endif %}">
      <div class="mb-3">
        {% if FieldAttr.type == "switch" %}
        <label class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="{{ FieldAttr.id }}" {% if Config.get(Type) and Config.get(Type).get(FieldId) %}checked{% endif %}>
          <span class="form-check-label">{{ FieldAttr.title }}
            {% if FieldAttr.tooltip %}
            <span class="form-help" title="{{ FieldAttr.tooltip }}" data-bs-toggle="tooltip" data-bs-html="true">?</span>
            {% endif %}
          </span>
        </label>
        {% else %}
        <label class="form-label {% if FieldAttr.required %}required{% endif %}">{{ FieldAttr.title }}
          {% if FieldAttr.tooltip %}
          <span class="form-help" title="{{ FieldAttr.tooltip }}" data-bs-toggle="tooltip" data-bs-html="true">?</span>
          {% endif %}
        </label>
          {% if FieldAttr.type == "select" %}
          <select class="form-select" id="{{ FieldAttr.id }}">
            {% for OptionValue, OptionTitle in FieldAttr.options.items() %}
            <option value="{{ OptionValue }}" {% if Config.get(Type) and Config.get(Type).get(FieldId) == OptionValue %}selected{% endif %}>{{ OptionTitle }}</option>
            {% endfor %}
          </select>
          {% else %}
          <input type="{{ FieldAttr.type }}" id="{{ FieldAttr.id }}" class="form-control" value="{% if Config.get(Type) %}{{ Config.get(Type).get(FieldId) or '' }}{% endif %}" placeholder="{{ FieldAttr.placeholder }}">
          {% endif %}
        {% endif %}
      </div>
    </div>
    {% if loop.last or loop.index%2 == 0 %}
    </div>
    {% endif %}
  {% endfor %}
{% endmacro %}

<!-- 生成空的表单元素 -->
{% macro gen_form_empty_elements(Fields) %}
  {% for FieldId, FieldAttr in Fields.items() %}
    {% if loop.index%2 == 1 %}
    <div class="row">
    {% endif %}
      <div class="{% if FieldAttr.type == "switch" %}col-12{% else %}col-lg-6{% endif %}">
        <div class="mb-3">
          {% if FieldAttr.type == "switch" %}
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="{{ FieldAttr.id }}" {% if FieldAttr.default %}checked{% endif %}>
            <span class="form-check-label">{{ FieldAttr.title }}
              {% if FieldAttr.tooltip %}
              <span class="form-help" title="{{ FieldAttr.tooltip }}" data-bs-toggle="tooltip" data-bs-html="true">?</span>
              {% endif %}
            </span>
          </label>
          {% else %}
            <label class="form-label {% if FieldAttr.required %}required{% endif %}">{{ FieldAttr.title }}
              {% if FieldAttr.tooltip %}
              <span class="form-help" title="{{ FieldAttr.tooltip }}" data-bs-toggle="tooltip" data-bs-html="true">?</span>
              {% endif %}
            </label>
            {% if FieldAttr.type == "select" %}
              <select class="form-select" id="{{ FieldAttr.id }}">
                {% for OptionValue, OptionTitle in FieldAttr.options.items() %}
                <option value="{{ OptionValue }}" {% if FieldAttr.default == OptionValue %}selected{% endif %}>{{ OptionTitle }}</option>
                {% endfor %}
              </select>
            {% else %}
            <input type="{{ FieldAttr.type }}" id="{{ FieldAttr.id }}" class="form-control" value="{{ FieldAttr.default or '' }}" placeholder="{{ FieldAttr.placeholder }}">
            {% endif %}
          {% endif %}
        </div>
      </div>
    {% if loop.last or loop.index%2 == 0 %}
    </div>
    {% endif %}
  {% endfor %}
{% endmacro %}

<!-- 生成过滤的下拉选项 -->
{% macro gen_recommend_filter_dropdown(Fields, Params) %}
  {% if Fields %}
  {% for FieldId, FieldAttr in Fields.items() %}
    {% if FieldAttr.type == "dropdown" %}
    <div class="dropdown">
      <button class="btn btn-ghost-secondary ms-auto dropdown-toggle px-1"
         data-bs-toggle="dropdown" aria-expanded="false">
        {% if not Params or not Params.get(FieldId) %}
          {{ FieldAttr.name }}
        {% else %}
          {% for Option in FieldAttr.options %}
            {% if Option.value == Params.get(FieldId) %}
              {{ Option.name }}
            {% endif %}
          {% endfor %}
        {% endif %}
      </button>
      <div class="dropdown-menu dropdown-menu-end">
        {% for Option in FieldAttr.options %}
        <a class="dropdown-item" href='javascript:filter_refresh("{{ FieldId }}", "{{ Option.value }}")' >
          {{ Option.name }}
        </a>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  {% endfor %}
  {% endif %}
{% endmacro %}
