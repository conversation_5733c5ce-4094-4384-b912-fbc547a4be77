import os.path
import re

from app.utils.media_utils import MediaUtils
import log
from app.downloader import Downloader
from app.helper import ProgressHelper
from app.helper.openai_helper import OpenAiHelper
from app.indexer import Indexer
from app.media import Media
from app.message import Message
from app.searcher import Searcher
from app.sites import Sites
from app.subscribe import Subscribe
from app.utils import StringUtils
from app.utils.types import SearchType, IndexerType, ProgressKey, RssType
from config import Config
from web.backend.web_utils import WebUtils

SEARCH_MEDIA_CACHE = {}
SEARCH_MEDIA_TYPE = {}


def search_medias_for_web(content, ident_flag=True, filters=None, tmdbid=None, media_type=None):
    """
    WEB资源搜索
    :param content: 关键字文本，可以包括 类型、标题、季、集、年份等信息，使用 空格分隔，也支持种子的命名格式
    :param ident_flag: 是否进行媒体信息识别
    :param filters: 其它过滤条件
    :param tmdbid: TMDBID或DB:豆瓣ID
    :param media_type: 媒体类型，配合tmdbid传入
    :return: 错误码，错误原因，成功时直接插入数据库
    """
    mtype, key_word, season_num, episode_num, year, content = StringUtils.get_keyword_from_string(content)
    if not key_word:
        log.info("【Web】%s 搜索关键字有误！" % content)
        return -1, "%s 未识别到搜索关键字！" % content
    # 类型
    if media_type:
        mtype = media_type
    # 开始进度
    _searcher = Searcher()
    _process = ProgressHelper()
    _media = Media()
    _process.start(ProgressKey.Search)
    # 识别媒体
    media_info = None
    if ident_flag:

        # 有TMDBID或豆瓣ID
        if tmdbid:
            media_info = WebUtils.get_mediainfo_from_id(mediaid=tmdbid, mtype=mtype)
        else:
            # 按输入名称查
            media_info = _media.get_media_info(mtype=media_type or mtype,
                                               title=content)

        # 整合集
        if media_info:
            if season_num:
                media_info.begin_season = int(season_num)
            if episode_num:
                media_info.begin_episode = int(episode_num)

        if media_info and media_info.tmdb_info:
            # 查询到TMDB信息
            log.info(f"【Web】从TMDB中匹配到{media_info.type.value}：{media_info.get_title_string()}")
            # 查找的季
            if media_info.begin_season is None:
                search_season = None
            else:
                search_season = media_info.get_season_list()
            # 查找的集
            search_episode = media_info.get_episode_list()
            if search_episode and not search_season:
                search_season = [1]
            # 中文名
            if media_info.cn_name:
                search_cn_name = media_info.cn_name
            else:
                search_cn_name = media_info.title

            first_search_name = search_cn_name

            season_name = None
            if season_num and 1 < season_num <= len(media_info.tmdb_info.seasons):
                match_season = next(filter(lambda x: x.season_number == season_num, media_info.tmdb_info.seasons), None)
                if match_season and match_season.name:
                    season_name = match_season.name

            filter_args = {"season": search_season,
                           "season_name": season_name,
                           "episode": search_episode,
                           "year": media_info.year,
                           "type": media_info.type}
        else:
            # 查询不到数据，使用快速搜索
            log.info(f"【Web】{content} 未从TMDB匹配到媒体信息，将使用快速搜索...")
            ident_flag = False
            media_info = None
            first_search_name = key_word
            filter_args = {
                "season": season_num,
                "episode": episode_num,
                "year": year
            }
    # 快速搜索
    else:
        first_search_name = key_word
        filter_args = {
            "season": season_num,
            "episode": episode_num,
            "year": year
        }
    # 整合高级查询条件
    if filters:
        filter_args.update(filters)
    # 开始搜索
    log.info("【Web】开始搜索 %s ..." % content)
    media_list = _searcher.search_medias(key_word=first_search_name,
                                         filter_args=filter_args,
                                         match_media=media_info,
                                         in_from=SearchType.WEB)

    # 清空缓存结果
    _searcher.delete_all_search_torrents()
    # 结束进度
    _process.end(ProgressKey.Search)
    if len(media_list) == 0:
        log.info("【Web】%s 未搜索到任何资源" % content)
        return 1, "%s 未搜索到任何资源" % content
    else:
        log.info("【Web】共搜索到 %s 个有效资源" % len(media_list))
        # 插入数据库
        media_list = sorted(media_list, key=lambda x: x.get_sort_str(), reverse=True)
        _searcher.insert_search_results(media_items=media_list,
                                        ident_flag=ident_flag,
                                        title=content)
        return 0, ""


def search_media_by_message(input_str, in_from: SearchType, user_id, user_name=None):
    """
    输入字符串，解析要求并进行资源搜索
    :param input_str: 输入字符串，可以包括标题、年份、季、集的信息，使用空格隔开
    :param in_from: 搜索下载的请求来源
    :param user_id: 需要发送消息的，传入该参数，则只给对应用户发送交互消息
    :param user_name: 用户名称
    :return: 请求的资源是否全部下载完整、请求的文本对应识别出来的媒体信息、请求的资源如果是剧集，则返回下载后仍然缺失的季集信息
    """
    global SEARCH_MEDIA_TYPE
    global SEARCH_MEDIA_CACHE

    if not input_str:
        log.info("【Searcher】搜索关键字有误！")
        return
    else:
        input_str = str(input_str).strip()
    # 如果是数字，表示选择项
    if input_str.isdigit() and int(input_str) < 10:
        # 获取之前保存的可选项
        choose = int(input_str) - 1
        if not SEARCH_MEDIA_CACHE.get(user_id) or \
                choose < 0 or choose >= len(SEARCH_MEDIA_CACHE.get(user_id)):
            Message().send_channel_msg(channel=in_from,
                                       title="输入有误！",
                                       user_id=user_id)
            log.warn("【Web】错误的输入值：%s" % input_str)
            return
        media_info = SEARCH_MEDIA_CACHE[user_id][choose]
        if not SEARCH_MEDIA_TYPE.get(user_id) \
                or SEARCH_MEDIA_TYPE.get(user_id) == "SEARCH":
            # 如果是豆瓣数据，需要重新查询TMDB的数据
            log.info("【message】豆瓣id: %s" % media_info.douban_id)
            if media_info.douban_id:
                _title = media_info.get_title_string()
                # 重新根据豆瓣ID查询媒体数据
                media_info = WebUtils.get_mediainfo_from_id('DB:' + media_info.douban_id, media_info.type)

                if not media_info or not media_info.tmdb_info:
                    Message().send_channel_msg(channel=in_from,
                                               title="%s 从TMDB查询不到媒体信息！" % _title,
                                               user_id=user_id)
                    return
            # 搜索
            __search_media(in_from=in_from,
                           media_info=media_info,
                           user_id=user_id,
                           user_name=user_name)
        else:
            # 订阅
            __rss_media(in_from=in_from,
                        media_info=media_info,
                        user_id=user_id,
                        user_name=user_name)
    # 接收到文本
    else:
        if input_str.startswith("订阅"):
            # 订阅
            SEARCH_MEDIA_TYPE[user_id] = "SUBSCRIBE"
            input_str = re.sub(r"订阅[:：\s]*", "", input_str)
        elif input_str.startswith("http"):
            # 下载链接
            SEARCH_MEDIA_TYPE[user_id] = "DOWNLOAD"
        elif OpenAiHelper().get_state() \
                and not input_str.startswith("搜索") \
                and not input_str.startswith("下载"):
            # 开启ChatGPT时，不以订阅、搜索、下载开头的均为聊天模式
            SEARCH_MEDIA_TYPE[user_id] = "ASK"
        else:
            # 搜索
            input_str = re.sub(r"(搜索|下载)[:：\s]*", "", input_str)
            SEARCH_MEDIA_TYPE[user_id] = "SEARCH"

        # 下载链接
        if SEARCH_MEDIA_TYPE[user_id] == "DOWNLOAD":
            # 检查是不是有这个站点
            site_info = Sites().get_sites(siteurl=input_str)
            # 偿试下载种子文件
            filepath, content, retmsg = Downloader().save_torrent_file(
                url=input_str,
                cookie=site_info.get("cookie"),
                ua=site_info.get("ua"),
                proxy=site_info.get("proxy")
            )
            # 下载种子出错
            if (not content or not filepath) and retmsg:
                Message().send_channel_msg(channel=in_from,
                                           title=retmsg,
                                           user_id=user_id)
                return
            # 识别文件名
            filename = os.path.basename(filepath)
            # 识别
            meta_info = Media().get_media_info(title=filename)
            if not meta_info:
                Message().send_channel_msg(channel=in_from,
                                           title="无法识别种子文件名！",
                                           user_id=user_id)
                return
            # 开始下载
            meta_info.set_torrent_info(enclosure=input_str)
            Downloader().download(media_info=meta_info,
                                  torrent_file=filepath,
                                  in_from=in_from,
                                  user_name=user_name)
        # 聊天
        elif SEARCH_MEDIA_TYPE[user_id] == "ASK":
            # 调用ChatGPT Api
            answer = OpenAiHelper().get_answer(text=input_str,
                                               userid=user_id)
            if not answer:
                answer = "ChatGTP出错了，请检查OpenAI API Key是否正确，如需搜索电影/电视剧，请发送 搜索或下载 + 名称"
            # 发送消息
            Message().send_channel_msg(channel=in_from,
                                       title="",
                                       text=str(answer).strip(),
                                       user_id=user_id)
        # 搜索或订阅
        else:
            # 获取字符串中可能的RSS站点列表
            rss_sites, content = StringUtils.get_idlist_from_string(input_str,
                                                                    [{
                                                                        "id": site.get("name"),
                                                                        "name": site.get("name")
                                                                    } for site in Sites().get_sites(rss=True)])

            # 索引器类型
            indexer_type = Indexer().get_client_type()
            indexers = Indexer().get_indexers()

            # 获取字符串中可能的搜索站点列表
            if indexer_type == IndexerType.BUILTIN:
                content = input_str
                search_sites, _ = StringUtils.get_idlist_from_string(input_str, [{
                    "id": indexer.name,
                    "name": indexer.name
                } for indexer in indexers])
            else:
                search_sites, content = StringUtils.get_idlist_from_string(content, [{
                    "id": indexer.name,
                    "name": indexer.name
                } for indexer in indexers])

            # 获取字符串中可能的下载设置
            download_setting, content = StringUtils.get_idlist_from_string(content, [{
                "id": dl.get("id"),
                "name": dl.get("name")
            } for dl in Downloader().get_download_setting().values()])
            if download_setting:
                download_setting = download_setting[0]

            # 识别媒体信息，列出匹配到的所有媒体
            log.info("【Web】正在识别 %s 的媒体信息..." % content)
            if not content:
                Message().send_channel_msg(channel=in_from,
                                           title="无法识别搜索内容！",
                                           user_id=user_id)
                return

            # 搜索名称
            medias = WebUtils.search_media_infos(
                keyword=content
            )
            if not medias:
                # 查询不到媒体信息
                Message().send_channel_msg(channel=in_from,
                                           title="%s 查询不到媒体信息！" % content,
                                           user_id=user_id)
                return

            # 保存识别信息到临时结果中，由于消息长度限制只取前8条
            SEARCH_MEDIA_CACHE[user_id] = []
            for meta_info in medias[:8]:
                # 合并站点和下载设置信息
                meta_info.rss_sites = rss_sites
                meta_info.search_sites = search_sites
                meta_info.set_download_info(download_setting=download_setting)
                SEARCH_MEDIA_CACHE[user_id].append(meta_info)

            if 1 == len(SEARCH_MEDIA_CACHE[user_id]):
                # 只有一条数据，直接开始搜索
                media_info = SEARCH_MEDIA_CACHE[user_id][0]
                if not SEARCH_MEDIA_TYPE.get(user_id) \
                        or SEARCH_MEDIA_TYPE.get(user_id) == "SEARCH":
                    # 如果是豆瓣数据，需要重新查询TMDB的数据
                    log.info("【message】豆瓣id: %s" % media_info.douban_id)
                    if media_info.douban_id:
                        _title = media_info.get_title_string()
                        media_info = media_info = WebUtils.get_mediainfo_from_id('DB:' + media_info.douban_id, mtype=media_info.type)
                        if not media_info or not media_info.tmdb_info:
                            Message().send_channel_msg(channel=in_from,
                                                       title="%s 从TMDB查询不到媒体信息！" % _title,
                                                       user_id=user_id)
                            return
                    # 发送消息
                    Message().send_channel_msg(channel=in_from,
                                               title=media_info.get_title_vote_string(),
                                               text=media_info.get_overview_string(),
                                               image=media_info.get_message_image(),
                                               url=media_info.get_detail_url(),
                                               user_id=user_id)
                    # 开始搜索
                    __search_media(in_from=in_from,
                                   media_info=media_info,
                                   user_id=user_id,
                                   user_name=user_name)
                else:
                    # 添加订阅
                    __rss_media(in_from=in_from,
                                media_info=media_info,
                                user_id=user_id,
                                user_name=user_name)
            else:
                # 发送消息通知选择
                Message().send_channel_list_msg(channel=in_from,
                                                title="共找到%s条相关信息，请回复对应序号" % len(
                                                    SEARCH_MEDIA_CACHE[user_id]),
                                                medias=SEARCH_MEDIA_CACHE[user_id],
                                                user_id=user_id)


def __search_media(in_from, media_info, user_id, user_name=None):
    """
    开始搜索和发送消息
    """
    # 检查是否存在，电视剧返回不存在的集清单
    exist_flag, no_exists, messages = Downloader().check_exists_medias(meta_info=media_info)
    if messages:
        Message().send_channel_msg(channel=in_from,
                                   title="\n".join(messages),
                                   user_id=user_id)
    # 已经存在
    if exist_flag:
        return

    # 开始搜索
    Message().send_channel_msg(channel=in_from,
                               title="开始搜索 %s ..." % media_info.title,
                               user_id=user_id)
    search_result, no_exists, search_count, download_count = Searcher().search_one_media(media_info=media_info,
                                                                                         in_from=in_from,
                                                                                         no_exists=no_exists,
                                                                                         sites=media_info.search_sites,
                                                                                         user_name=user_name)
    # 没有搜索到数据
    if not search_count:
        Message().send_channel_msg(channel=in_from,
                                   title="%s 未搜索到任何资源" % media_info.title,
                                   user_id=user_id)
    else:
        # 搜索到了但是没开自动下载
        if download_count is None:
            Message().send_channel_msg(channel=in_from,
                                       title="%s 共搜索到%s个资源，点击选择下载" % (media_info.title, search_count),
                                       image=media_info.get_message_image(),
                                       url="search",
                                       user_id=user_id)
            return
        else:
            # 搜索到了但是没下载到数据
            if download_count == 0:
                Message().send_channel_msg(channel=in_from,
                                           title="%s 共搜索到%s个结果，但没有下载到任何资源" % (
                                               media_info.title, search_count),
                                           user_id=user_id)
    # 没有下载完成，且打开了自动添加订阅
    if not search_result and Config().get_config('pt').get('search_no_result_rss'):
        # 添加订阅
        __rss_media(in_from=in_from,
                    media_info=media_info,
                    user_id=user_id,
                    state='R',
                    user_name=user_name)


def __rss_media(in_from, media_info, user_id=None, state='D', user_name=None):
    """
    开始添加订阅和发送消息
    """
    # 添加订阅
    mediaid = f"DB:{media_info.douban_id}" if media_info.douban_id else media_info.tmdb_id
    code, msg, media_info = Subscribe().add_rss_subscribe(media_info=media_info,
                                                          mtype=media_info.type,
                                                          name=media_info.title,
                                                          year=media_info.year,
                                                          channel=RssType.Auto,
                                                          season=media_info.begin_season,
                                                          mediaid=mediaid,
                                                          state=state,
                                                          rss_sites=media_info.rss_sites,
                                                          search_sites=media_info.search_sites,
                                                          download_setting=media_info.download_setting,
                                                          in_from=in_from,
                                                          user_name=user_name)
    if code == 0:
        log.info("【Web】%s %s 已添加订阅" % (media_info.type.value, media_info.get_title_string()))
    else:
        if in_from in Message().get_search_types():
            log.info("【Web】%s 添加订阅失败：%s" % (media_info.title, msg))
            Message().send_channel_msg(channel=in_from,
                                       title="%s 添加订阅失败：%s" % (media_info.title, msg),
                                       user_id=user_id)
